###################
### MY PROGRAMS ###
###################

# Set programs that you use
$terminal = kitty
$fileManager = thunar
$menu = rofi -show drun
$browser = zen-browser
####################
### KEYBINDINGSS ###
####################

$mainMod = SUPER # Sets "Windows" key as main modifier

bind = $mainMod, RETURN, exec, $terminal tmux
bind = $mainMod SHIFT, RETURN, exec, [float; size 700 600] $terminal tmux 
bind = $mainMod, Q, killactive,
bind = $mainMod, M, exec, sh ~/scripts/open_yazi.sh
bind = $mainMod SHIFT, M, exec, [float; size 525 260] sh ~/scripts/open_yazi.sh 
bind = $mainMod, T, togglefloating,
bind = $mainMod, SPACE, exec, $menu
bind = $mainMod, P, exec, sh ~/scripts/power_management.sh
bind = $mainMod, SLASH, togglesplit,
bind = $mainMod, R, exec, sh ~/scripts/launch.sh
bind = $mainMod, A, exec, sh ~/scripts/hyprpaper_reload.sh
bind = $mainMod, B, exec, $browser
bind = $mainMod, F, fullscreen
bind = $mainMod SHIFT, W, exec, sh ~/scripts/select_wallpaper.sh 
bind = $mainMod, W, exec, sh ~/scripts/launch_widgets.sh
bind = $mainMod, E, exec, $fileManager

# Screen Shot
bind = $mainMod, PRINT, exec, hyprshot -m output
bind = $mainMod SHIFT, PRINT, exec, hyprshot -m region

# Window focus
bind = $mainMod, h, movefocus, l
bind = $mainMod, l, movefocus, r
bind = $mainMod, k, movefocus, u
bind = $mainMod, j, movefocus, d

# Move Window
bind = $mainMod SHIFT, h, movewindow, l
bind = $mainMod SHIFT, l, movewindow, r
bind = $mainMod SHIFT, k, movewindow, u
bind = $mainMod SHIFT, j, movewindow, d 

# Resize Window
bind = $mainMod ALT, h, resizeactive, -40 0
bind = $mainMod ALT, l, resizeactive, 40 0
bind = $mainMod ALT, k, resizeactive, 0 -40
bind = $mainMod ALT, j, resizeactive, 0 40

# Move Floating Window
binde = $mainMod CTRL, h, moveactive, -40 0
binde = $mainMod CTRL, l, moveactive, 40 0
binde = $mainMod CTRL, k, moveactive, 0 -40
binde = $mainMod CTRL, j, moveactive, 0 40

# Volume and Media Control
bind = , XF86AudioRaiseVolume, exec, pamixer -i 5 
bind = , XF86AudioLowerVolume, exec, pamixer -d 5 
bind = , XF86AudioMicMute, exec, pamixer --default-source -t
bind = , XF86AudioMute, exec, pamixer -t

# Brightness Control
bind = , XF86MonBrightnessUp, exec, brightnessctl s +5%
bind = , XF86MonBrightnessDown, exec, brightnessctl s 5%-

# Switch workspaces with mainMod + [0-9]
bind = $mainMod, 1, workspace, 1
bind = $mainMod, 2, workspace, 2
bind = $mainMod, 3, workspace, 3
bind = $mainMod, 4, workspace, 4
bind = $mainMod, 5, workspace, 5
bind = $mainMod, 6, workspace, 6
bind = $mainMod, 7, workspace, 7
bind = $mainMod, 8, workspace, 8
bind = $mainMod, 9, workspace, 9
bind = $mainMod, 0, workspace, 10

# Move active window to a workspace with mainMod + SHIFT + [0-9]
bind = $mainMod SHIFT, 1, movetoworkspace, 1
bind = $mainMod SHIFT, 2, movetoworkspace, 2
bind = $mainMod SHIFT, 3, movetoworkspace, 3
bind = $mainMod SHIFT, 4, movetoworkspace, 4
bind = $mainMod SHIFT, 5, movetoworkspace, 5
bind = $mainMod SHIFT, 6, movetoworkspace, 6
bind = $mainMod SHIFT, 7, movetoworkspace, 7
bind = $mainMod SHIFT, 8, movetoworkspace, 8
bind = $mainMod SHIFT, 9, movetoworkspace, 9
bind = $mainMod SHIFT, 0, movetoworkspace, 10

# Example special workspace (scratchpad)
# bind = $mainMod, S, togglespecialworkspace, magic
# bind = $mainMod SHIFT, S, movetoworkspace, special:magic

# Scroll through existing workspaces with mainMod + scroll
bind = $mainMod, mouse_down, workspace, e+1
bind = $mainMod, mouse_up, workspace, e-1

# Switch to the next/previous workspace with mainMod + arrow keys
bind = $mainMod, up, workspace, e-1
bind = $mainMod, down, workspace, e+1

# Move/resize windows with mainMod + LMB/RMB and dragging
bindm = $mainMod, mouse:272, movewindow
bindm = $mainMod, mouse:273, resizewindow

# Lock screen
bind = $mainMod, ESCAPE, exec, hyprlock