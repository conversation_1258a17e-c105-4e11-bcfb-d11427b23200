# source = ~/.config/hypr/myColors.conf
source = ~/.config/hypr/keybinds.conf
source = ~/.config/hypr/colors-wal.conf


################
### MONITORS ###
################

monitor=,highrr,auto,1
monitor=HDMI-A-2,1920x1080,auto,1,mirror 

#################
### AUTOSTART ###
#################

exec-once = hyprpaper
exec-once = eww open-many side-bar notifications
exec-once = dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP
exec-once = hypridle
#############################
## ENVIRONMENT VARIABLES ###
#############################

# See https://wiki.hyprland.org/Configuring/Environment-variables/

env = XCURSOR_SIZE,28
env = XCURSOR_THEME,macOS
env = HYPRCURSOR_SIZE,28
env = HYPRCURSOR_THEME,macOS
env = XDG_CURRENT_DESKTOP,Hyprland
env = XDG_SESSION_DESKTOP,Hyprland

#####################
### LOOK AND FEEL ###
#####################

general { 
    gaps_in = 2
    gaps_out = 4

    border_size = 2

    # https://wiki.hyprland.org/Configuring/Variables/#variable-types for info about colors
    # Using wal-generated colors via variables
    col.active_border = rgba($active_borderff)
    col.inactive_border = rgba($inactive_borderff)

    # Set to true enable resizing windows by clicking and dragging on borders and gaps
    resize_on_border = false 

    # Please see https://wiki.hyprland.org/Configuring/Tearing/ before you turn this on
    allow_tearing = false

    layout = dwindle
}

# https://wiki.hyprland.org/Configuring/Variables/#decoration
decoration {
    rounding = 6

    # Change transparency of focused and unfocused windows
    active_opacity = 1.0
    inactive_opacity = 1.0

    # https://wiki.hyprland.org/Configuring/Variables/#blur
    blur {
        enabled = false
        vibrancy = 0.8
        contrast = 0.6
        size = 8
        passes = 2
        ignore_opacity = false
    }

    shadow{
        enabled = false
        sharp = false
    }
    #blurls = gtk-layer-shell
    
}

# https://wiki.hyprland.org/Configuring/Variables/#animations
animations {
    enabled = true

    # Default animations, see https://wiki.hyprland.org/Configuring/Animations/ for more

    bezier = myBezier, 0.05, 0.8, 0.1, 1

    animation = windows, 1, 6, myBezier
    animation = windowsOut, 1, 7, default, popin 90%
    animation = fade, 1, 7, default
    animation = workspaces, 1, 6, default, slidevert
}

# See https://wiki.hyprland.org/Configuring/Dwindle-Layout/ for more
dwindle {
    pseudotile = true # Master switch for pseudotiling. Enabling is bound to mainMod + P in the keybinds section below
    preserve_split = true # You probably want this
}

# See https://wiki.hyprland.org/Configuring/Master-Layout/ for more
master {
    new_status = master
}

# https://wiki.hyprland.org/Configuring/Variables/#misc
misc { 
    force_default_wallpaper = 0 # Set to 0 or 1 to disable the anime mascot wallpapers
    disable_hyprland_logo = true # If true disables the random hyprland logo / anime girl background. :(
}


#############
### INPUT ###
#############

# https://wiki.hyprland.org/Configuring/Variables/#input
input {
    kb_layout = us
    kb_variant = 
    kb_model =
    kb_options =
    kb_rules =

    follow_mouse = 1

    sensitivity = 0 # -acceleration = false1.0 - 1.0, 0 means no modification.
    accel_profile = flat

    touchpad {
        natural_scroll = true
    }
}

# https://wiki.hyprland.org/Configuring/Variables/#gestures
gestures {
    workspace_swipe = true
}

# Example per-device config
# See https://wiki.hyprland.org/Configuring/Keywords/#per-device-input-configs for more
device {
    name = epic-mouse-v1
    sensitivity = -0.5
}

##############################
### WINDOWS AND WORKSPACES ###
##############################

# See https://wiki.hyprland.org/Configuring/Window-Rules/ for more
# See https://wiki.hyprland.org/Configuring/Workspace-Rules/ for workspace rules

# Example windowrule v1
# windowrule = float, ^(kitty)$

# layerrule=ignorezero, gtk-layer-shell
# layerrule=blur, gtk-layer-shell
# Example windowrule v2
# windowrulev2 = float,class:^(kitty)$,title:^(kitty)$

windowrulev2 = suppressevent maximize, class:.* # You'll probably like this.

# Ignore Zen browser notifications
windowrulev2 = noinitialfocus, class:^(zen-alpha)$, title:^(.*notification.*)$
windowrulev2 = nofocus, class:^(zen-alpha)$, title:^(.*notification.*)$
windowrulev2 = float, class:^(zen-alpha)$, title:^(.*notification.*)$
windowrulev2 = move 100%-0, class:^(zen-alpha)$, title:^(.*notification.*)$
