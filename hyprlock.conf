
background {
    monitor =
    path = /home/<USER>/wallpapers/982.jpg
    color = rgba(25, 20, 20, 0.8)

    # all these options are taken from hyprland, see https://wiki.hyprland.org/Configuring/Variables/#blur for explanations
    blur_passes = 0 # 0 disables blurring
    blur_size = 0
    noise = 0
    contrast = 0
    brightness = 0.7
    vibrancy = 0.9
    vibrancy_darkness = 0.8
}

input-field {
    monitor =
    size = 120, 35
    outline_thickness = 2
    dots_size = 0.27 # Scale of input-field height, 0.2 - 0.8
    dots_spacing = 0.25 # Scale of dots' absolute size, 0.0 - 1.0
    dots_center = false
    dots_rounding = -1 # -1 default circle, -2 follow input-field rounding
    outer_color = rgba(15,15,15,0)
    inner_color = rgba(20, 20, 20, 0.8)
    font_color = rgba(255, 255, 255, 0.8)
    fade_on_empty = true
    fade_timeout = 1000 # Milliseconds before fade_on_empty is triggered.
    placeholder_text = # Text rendered in the input box when it's empty.
    hide_input = false
    rounding = -1 # -1 means complete rounding (circle/oval)
    check_color = rgb(204, 136, 34)
    fail_color = rgb(204, 34, 34) # if authentication failed, changes outer_color and fail message color
    fail_text = <i>$FAIL <b>($ATTEMPTS)</b></i> # can be set to empty
    fail_timeout = 2000 # milliseconds before fail_text and fail_color disappears
    fail_transition = 300 # transition time in ms between normal outer_color and fail_color
    capslock_color = -1
    numlock_color = -1
    bothlock_color = -1 # when both locks are active. -1 means don't change outer color (same for above)
    invert_numlock = false # change color if numlock is off
    swap_font_color = false # see below
    font_family= Mononoki

    position = 0, 0
    halign = center
    valign = center
}



label {
    monitor =
    text = <i>$USER</i>
    text_align = center # center/right or any value for default left. multi-line text alignment inside label container
    color = rgba(255, 255, 255, 0.9)
    font_size = 24
    font_family = iosevka
    rotate = 0 # degrees, counter-clockwise

    position = -65, 120
    halign = right
    valign = bottom
}

label {
    monitor =
    text = cmd[update:1000] echo "$TIME"
    text_align = center # center/right or any value for default left. multi-line text alignment inside label container
    color = rgba(255, 255, 255, 0.9)
    font_size = 56
    font_family = iosevka
    rotate = 0 # degrees, counter-clockwise

    position = -60, 40
    halign = right
    valign = bottom
}

