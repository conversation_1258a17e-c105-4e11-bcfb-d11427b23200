#!/bin/bash
# Script to update Hyprland colors from wal
# Run this after changing wallpaper with wal

# Source wal colors
source ~/.cache/wal/colors.sh

# Update the Hyprland colors file
cat > ~/.config/hypr/colors-wal.conf << EOF
# Wal colors for Hyprland - Auto-generated
# Last updated: $(date)

# Active border: using wal color5 (accent color)
\$active_border = ${cursor#\#}

# Inactive border: using wal background color  
\$inactive_border = ${background#\#}

# Alternative combinations you can uncomment:
# \$active_border = ${color1#\#}   # wal color1 (red)
# \$active_border = ${color3#\#}   # wal color3 (orange-red) 
# \$active_border = ${foreground#\#}   # wal foreground (light)
EOF

echo "Updated Hyprland colors from wal"
echo "Active border: ${color5}"
echo "Inactive border: ${background}"

# Reload Hyprland config
hyprctl reload
